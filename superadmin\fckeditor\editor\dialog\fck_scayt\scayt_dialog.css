html, body
{
	background-color: transparent;
	margin: 0px;
	padding: 0px;
}

body
{
	padding: 10px;
}

body, td, input, select, textarea
{
	font-size: 11px;
	font-family: 'Microsoft Sans Serif' , Arial, Helvetica, Verdana;
}

.midtext
{
	padding:0px;
	margin:10px;
}

.midtext p
{
	padding:0px;
	margin:10px;
}

.Button
{
	border: #737357 1px solid;
	color: #3b3b1f;
	background-color: #c7c78f;
}

.PopupTabArea , .button
{
	color: #737357;
	background-color: #e3e3c7;
}

.PopupTitleBorder
{
	border-bottom: #d5d59d 1px solid;
}
.PopupTabEmptyArea
{
	padding-left: 10px;
	border-bottom: #d5d59d 1px solid;
}

.PopupTab, .PopupTabSelected
{
	border-right: #d5d59d 1px solid;
	border-top: #d5d59d 1px solid;
	border-left: #d5d59d 1px solid;
	padding: 3px 5px 3px 5px;
	color: #737357;
}

.PopupTab
{
	margin-top: 1px;
	border-bottom: #d5d59d 1px solid;
	cursor: pointer;
	cursor: hand;
}

.PopupTabSelected
{
	font-weight: bold;
	cursor: default;
	padding-top: 4px;
	border-bottom: #f1f1e3 1px solid;
	background-color: #f1f1e3;
}

ul {
    padding:0;
    margin:0px 0px 12px 0px;
    list-style-type:none;
}
ul.tabs {
    height:20px;
    margin:10px 0px;
}
ul.tabs li {
    float: left;
	display:none;
}
div.tab_container {
    /*display:none;*/
    padding: 0px 5px ;
}
.lcol {
    float:left;
    width:47%;
    margin-left:5px;
}
.rcol {
    float:right;
    width:47%;
    margin-right:5px;
}
div.tabs-container{
	height:220px;
	overflow-x:hidden;
	overflow-y:auto;
}

div.tabs-container h3{
    margin:5px 15px 7px 15px;
    background-color:transparent;
    font-size: 14px ;
}

.li {
    border: 1px solid transparent;
}

#dic_message{
	height: 24px;
}
#dic_message .error{
	color: red ;
}
#dic_message .success{
	color: blue ;
}

.dic_buttons {
	margin-top: 5px;
	padding-left:10px;
}
.dic_buttons a {
	display: none;
}
a.button {
	border: #d5d59d 1px solid;
	padding: 2px 4px;
	margin-right: 4px;
	text-decoration: none;
}

a.button:hover,
a.button:active,
a.button:visited{
	padding: 2px 4px;
	margin-right: 4px;
	text-decoration: none;
}
a.button:hover {
	border: #d5d59d 1px solid;
	color: #e3e3c7;
	background-color: #737357;
}

#scayt_options li {
	display: none;
}

#dic_info {
	margin:10px;
}
#dic_tab {
	display:none;
}
