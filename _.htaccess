RewriteEngine On
RewriteBase /
RewriteCond %{HTTP_HOST} !^camauairport.vn$ [NC]
RewriteRule ^(.*)$ http://camauairport.vn/$1 [L,R=301]

RewriteCond %{REQUEST_METHOD} ^TRACE
RewriteRule .* - [F]

RewriteCond %{REQUEST_METHOD} ^TRACE
RewriteRule .* - [F]

RewriteRule ^linh-kien-([^/]+)-cua-hang-([^/]+)-([0-9]*+)-([0-9]*+).html cate.php?id=$3&nsx=$4
RewriteRule ^linh-kien-([0-9]*+)-([^/]+).html cate.php?id=$1

RewriteRule ^([0-9]*+)-([^/]+).html entry.php?id=$1
RewriteRule ^([^/]+)-([0-9]*+).html intro.php?id=$2

RewriteRule ^lien-he.html feedback.php

ErrorDocument 500 'Internal Server Error'
ErrorDocument 404 'Not Found'
ErrorDocument 403 'Forbidden'
ErrorDocument 401 'Auth Required'
ErrorDocument 400 'Bad Request'

# BEGIN GZIP
<ifmodule mod_deflate.c>
AddOutputFilterByType DEFLATE text/text text/html text/plain text/xml text/css application/x-javascript application/javascript
</ifmodule>
# END GZIP

Options -Indexes

# Security Headers
Header always append X-Frame-Options SAMEORIGIN
Header always set X-Content-Type-Options nosniff
Header always set X-XSS-Protection "1; mode=block"
Header always set Referrer-Policy "strict-origin-when-cross-origin"

# Block suspicious requests
RewriteCond %{QUERY_STRING} (eval\(|base64_decode|gzinflate) [NC,OR]
RewriteCond %{QUERY_STRING} (\.\.\/|\.\.\\) [NC,OR]
RewriteCond %{QUERY_STRING} (ftp:|http:|https:).*\.(txt|php|pl|py|exe|asp) [NC]
RewriteRule .* - [F,L]

# Block file injection attempts
<Files ~ "\.(txt|log|md|sql|bak|old|tmp)$">
    Order allow,deny
    Deny from all
</Files>

php_value session.cookie_httponly 1
php_value session.cookie_secure 0

php_flag zlib.output_compression on

<IfModule mod_expires.c>

# Enable expirations
ExpiresActive On

# Default directive
ExpiresDefault "access plus 1 month"

# My favicon
ExpiresByType image/x-icon "access plus 1 month"

# Images
ExpiresByType image/gif "access plus 1 month"
ExpiresByType image/png "access plus 1 month"
ExpiresByType image/jpg "access plus 1 month"
ExpiresByType image/jpeg "access plus 1 month"

# CSS
ExpiresByType text/css "access 1 week"

# Javascript
ExpiresByType application/javascript "access plus 1 month"

</IfModule>