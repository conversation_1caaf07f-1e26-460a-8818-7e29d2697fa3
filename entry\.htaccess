# Disable PHP execution
<FilesMatch "\.(?i:php|php3|php4|php5|phtml|cgi|pl|py|jsp|asp|htm|shtml|sh|cgi|suspected)$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# Only allow specific file types
<FilesMatch "\.(?i:jpg|jpeg|gif|png|pdf|doc|docx|xls|xlsx)$">
    Order Deny,Allow
    Allow from all
</FilesMatch>

# Prevent directory listing
Options -Indexes

# Prevent .htaccess modification
<Files .htaccess>
    Order Allow,Deny
    Deny from all
</Files>

# Additional security headers
<IfModule mod_headers.c>
    Header set X-Content-Type-Options "nosniff"
    Header set X-Frame-Options "SAMEORIGIN"
    Header set X-XSS-Protection "1; mode=block"
</IfModule>