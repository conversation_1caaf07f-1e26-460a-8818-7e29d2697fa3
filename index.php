<?php

require_once 'config.php';

require_once 'function.php';

include 'includes/header.php';

?>



<section id="page-home">

    <script type="text/javascript" src="<?php echo ASSETS_URL; ?>/js/flexslider.js"></script>

    <script type="text/javascript">

    $(document).ready(function() {

        $('.flexslider').flexslider({

            animation: "slide",

            start: function(slider) {

                $('body').removeClass('loading');

            }

        });

    });

    </script>

    <div class="module-slider">

        <div class="flexslider">

            <ul class="slides">

                <li><a href="https://hondaotocantho.com.vn/tin-tuc-51-chuong-trinh-khuyen-mai.html"><img

                            src="/OTO3602100212/files/khuyen-mai/khuyen-mai-xe-oto-honda-2025.jpg" alt="Banner 1" /></a></li>

            </ul>

        </div>

    </div>

    <div class="clr"></div>

    <div class="home-action background_primary">

        <div class="container">

            <div class="row">



                <div class="col col-4">

                    <a href="/tin-tuc.html">

                        <img src="<?php echo ASSETS_URL; ?>/images/parking.svg" alt="CHƯƠNG TRÌNH KHUYẾN MÃI" />

                        <h3 class="color_primary">CHƯƠNG TRÌNH KHUYẾN MÃI</h3>

                        <label>Khuyến mãi HOT NHẤT trong tháng</label>

                    </a>

                </div>

                <div class="col col-4">

                    <a href="javascript:void(0);" onclick="$('#panel').fadeIn();">

                        <img src="<?php echo ASSETS_URL; ?>/images/wheel.svg" alt="YÊU CẦU BÁO GIÁ" />

                        <h3 class="color_primary">ĐĂNG KÝ LÁI THỬ</h3>

                        <label>Trải nghiệm lái xe thực tế</label>

                    </a>

                </div>

                <div class="col col-4">

                    <a href="javascript:void(0);" onclick="$('#panel').fadeIn();">

                        <img src="<?php echo ASSETS_URL; ?>/images/dollar.svg" alt="YÊU CẦU BÁO GIÁ" />

                        <h3 class="color_primary">YÊU CẦU BÁO GIÁ</h3>

                        <label>Để nhận giá ưu đãi tốt nhất</label>

                    </a>

                </div>

            </div>

        </div>

    </div>

    <div class="clr"></div>

    <div class="container">

        <div class="mid-title">

            <div class="titleL">

                <h3 class="color_primary">Xe ô tô Honda</h3>

            </div>

            <div class="titleR"></div>

            <div class="clear"></div>

        </div>

        <div class="clr"></div>

        <div class="row home-car-list">

            <?php

                $sqlc = "SELECT DISTINCT b.category_id, b.category_name, b.position 

                        FROM entry2 a, category b 

                        WHERE a.category_id = b.category_id 

                        AND type = 0 

                        AND new = 1 

                        ORDER BY b.position ASC";

                $resultc = mysql_query($sqlc);



                while($rowc = mysql_fetch_array($resultc)) {

                    $categoryUrl = 'san-pham-' . $rowc['category_id'] . '-' . utf8tourl(utf8convert($rowc['category_name'])) . '.html';

                    

                    $sql = "SELECT * FROM entry2 

                            WHERE category_id=" . $rowc['category_id'] . " 

                            AND new = 1 

                            ORDER BY date_post DESC";

                    $result = mysql_query($sql);

                    

                    while($row = mysql_fetch_array($result)):

                        $carUrl = 'honda-' . $row['entry_id'] . '-' . utf8tourl(utf8convert($row['title'])) . '.html';

                ?>

            <div class="col col-3 center mb-50">

                <a class="grid-item" href="<?= $carUrl ?>">

                    <img src="<?= htmlspecialchars($row['image']) ?>" style="height: 105px;width: auto;max-width: 186.67px;object-fit: contain;"

                        alt="Xe Honda <?= htmlspecialchars($row['title']) ?>">

                    <h3>Xe Honda <?= htmlspecialchars($row['title']) ?></h3>

                    <div class="price"><b>Giá <?= $row['intro'] ?></b></div>

                </a>

            </div>

            <?php 

                    endwhile;

                }

                ?>

        </div>

        <a href="tel:<?php echo isset($contact['hotline']) ? $contact['hotline'] : '0919 905 293'; ?>" target="_self"

            class="btn-action background_primary"><img

                alt="<?php echo isset($contact['hotline']) ? $contact['hotline'] : '0919 905 293'; ?>"

                src="<?php echo ASSETS_URL; ?>/images/phone.svg">

            <span>Liên Hệ Tư Vấn :

                <?php echo isset($contact['hotline']) ? $contact['hotline'] : '0919 905 293'; ?></span>

        </a>

    </div>

    <div class="clr"></div>

    <div class="home-reason"

        style=" background: url(/content/HONDA/interior.jpg) no-repeat center center; background-size: cover; ">

        <div class="container">

            <div class="mid-title">

                <div class="titleL">

                    <h1 class="color_primary">TẠI SAO CHỌN Honda &Ocirc;t&ocirc; Cần Thơ</h1>

                </div>

                <div class="titleR"></div>

                <div class="clear"></div>

            </div>

            <div class="clr"></div>

            <img src="/OTO3602100212/files/hondaoto.cantho.vn.jpg" alt="Honda &Ocirc;t&ocirc; Cần Thơ">

            <div id="reasons" class="border_primary">

                <a class="background_primary" onclick="$('#reasons blockquote').slideUp(0);$('#reason-1').slideDown();"

                    href="javascript:void(0);">GIÁ ƯU ĐÃI - GIAO XE NHANH CHÓNG</a>

                <blockquote style="display:block;" id="reason-1">Honda &Ocirc;t&ocirc; Cần Thơ luôn cam kết mang lại mức

                    giá ưu đãi nhất cho qúy khách với thời gian giao xe nhanh nhất.</blockquote>

                <a class="background_primary" onclick="$('#reasons blockquote').slideUp(0);$('#reason-2').slideDown();"

                    href="javascript:void(0);">KHUYẾN MÃI NHIỀU NHẤT, TỐT NHẤT</a>

                <blockquote id="reason-2">Với hoạt động bán hàng sôi nổi, chúng tôi luôn cập nhật sớm nhất các chương

                    trình khuyến mãi của hãng và đại lý.</blockquote>

                <a class="background_primary" onclick="$('#reasons blockquote').slideUp(0);$('#reason-3').slideDown();"

                    href="javascript:void(0);">LÁI THỬ XE, KÝ HỢP ĐỒNG TẬN NHÀ</a>

                <blockquote id="reason-3">Honda &Ocirc;t&ocirc; Cần Thơ hỗ trợ Quý khách nhanh chóng trải nghiệm xe mà

                    không tốn thời gian.</blockquote>

                <a class="background_primary" onclick="$('#reasons blockquote').slideUp(0);$('#reason-4').slideDown();"

                    href="javascript:void(0);">HỖ TRỢ TƯ VẤN TẬN TÌNH, NHANH CHÓNG</a>

                <blockquote id="reason-4">Với hơn nhiều năm kinh nghiệm tư vấn trong ngành ô tô. Chúng tôi sẽ hỗ trợ Quý

                    Khách chọn được chiếc xe ô tô ưng ý nhất</blockquote>

                <a class="background_primary" onclick="$('#reasons blockquote').slideUp(0);$('#reason-5').slideDown();"

                    href="javascript:void(0);">MUA XE TRẢ GÓP - HỖ TRỢ LÊN TỚI 80%</a>

                <blockquote id="reason-5">Honda &Ocirc;t&ocirc; Cần Thơ – Bán xe trả góp LS thấp – Thủ tục nhanh chóng –

                    Hỗ trợ vay 80% giá trị xe (Tặng nhiều quà tặng hấp dẫn)</blockquote>

            </div>

        </div>

    </div>



    <div class="clr"></div>

    <div class="container">

        <div class="mid-title">

            <div class="titleL">

                <h3 class="color_primary">Thông tin mới</h3>

            </div>

            <div class="titleR"></div>



        </div>

        <div class="clear"></div>

        <?php

            $sql = "SELECT * FROM entry a 

                    JOIN category b ON a.category_id = b.category_id 

                    WHERE type = 1 

                    ORDER BY date_post DESC 

                    LIMIT 6";



            $result = mysql_query($sql);

            ?>

        <div class="row">

            <?php while($row = mysql_fetch_array($result)): 

                    $url = $row['entry_id'].'-'.utf8tourl(utf8convert($row['title'])).'.html';

                    $image = !empty($row['image']) ? $row['image'] : 'images/noimage.jpg';

                    $title = htmlspecialchars($row['title']);

                ?>

            <div class="col col-6">

                <a href="/<?= $url ?>" class="grid-article">

                    <div class="grid-article-img">

                        <div class="vp11" style="background-image: url('<?= $image ?>');">

                            <img src="<?= $image ?>" alt="<?= $title ?>">

                        </div>

                    </div>

                    <h3><?= $title ?></h3>

                    <p></p>

                </a>

            </div>

            <?php endwhile; ?>

        </div>

        <div class="clear"></div>

        <div class="row sales-block">

            <?php

                if (!isset($connect) || !$connect) {

                    die("Database connection failed");

                }

                // Fetch logo information

                $sql = "select * from picture where id=1;";

                $result = mysql_query($sql);

                $row = mysql_fetch_array($result);



                // Fetch contact information - with error handling

                $sql_contact = "SELECT `key`, `value` FROM config WHERE type='contact'";

                $result_contact = mysql_query($sql_contact) or die("Contact query failed: " . mysql_error());



                $contact = array();

                while ($row_contact = mysql_fetch_array($result_contact)) {

                    $contact[$row_contact['key']] = $row_contact['value'];

                }

                ?>

            <div class="col col-4">

                <img src="/OTO3602100212/files/mr_khoi.jpg" alt="Honda &Ocirc;t&ocirc; Cần Thơ">

            </div>

            <div class="col col-4">

                <p style="margin-bottom: 0px;"><strong>TƯ VẤN BÁN HÀNG</strong></p>

                <h2 style="font-size: 30px;color: #ec1a3b;text-transform: uppercase;margin: 5px 0 10px;"><strong

                        class="color_primary">Mr Minh Khôi</strong></h2>

                <div style=" float: left; width: 50px; height: 3px; background: #ccc; margin: 5px 0 20px; "></div>

                <div class="clear"></div>

                <p>Với kinh nghiệm tư vấn mua bán xe lâu năm. Hãy gọi ngay để được tư vấn chính xác và nhận được khuyến

                    mãi nhiều nhất.</p>

                <p>Chúng tôi <b>“luôn cung cấp mức giá tốt nhất, hậu mãi tốt nhất cho quý khách”</b></p>

                <a href="tel:0919 905 293"

                    style=" float: left; width: 100%; padding: 5px 7px; box-sizing: border-box; background: #ec1a3b;margin: 5px 0 20px"

                    class="background_primary hotline">

                    <img alt="0919 905 293" src="<?php echo ASSETS_URL; ?>/images/phone.svg"

                        style=" float: left; height: 30px; filter: invert(100%); ">

                    <span

                        style=" float: left; line-height: 30px; margin-left: 10px; color: #fff; text-transform: uppercase; font-weight: bold; font-size: 20px; ">Hotline

                        24/7: <?php echo isset($contact['hotline']) ? $contact['hotline'] : '0919 905 293'; ?></span>

                </a>

                <a href="mailto:<EMAIL>" target="_self" class="email"

                    style=" float: left; width: 100%; padding: 3px 7px; box-sizing: border-box; border: 2px solid #333; margin-bottom: 20px; ">

                    <img alt="<EMAIL>" src="<?php echo ASSETS_URL; ?>/images/email.svg"

                        style=" float: left; height: 30px;">

                    <span

                        style=" float: left; line-height: 30px; margin-left: 10px; text-transform: uppercase; font-weight: bold; font-size: 16px; "><?php echo isset($contact['email']) ? $contact['email'] : '<EMAIL>'; ?></span>

                </a>

            </div>

            <div class="col col-4">

                <p style="margin-bottom: 0px;"><strong>ĐIỀN FORM LIÊN HỆ</strong></p>

                <div style=" float: left; width: 50px; height: 3px; background: #ccc; margin: 5px 0 20px; "></div>

                <div class="clear"></div>

                <form method="post" id="contactForm">

                    <label>Họ & Tên</label>

                    <input type="text" name="name" required>

                    <div class="clear"></div>

                    <div class="row">

                        <div class="col col-6">

                            <label>Điện thoại</label>

                            <input type="text" name="phone" required>

                        </div>

                        <div class="col col-6">

                            <label>Email</label>

                            <input type="email" name="email">

                        </div>

                    </div>

                    <label>Nội dung</label>

                    <textarea name="content" required></textarea>

                    <button type="submit" name="task" class="background_primary" value="request">Gửi ngay</button>

                </form>

                <div id="responseMessage2"></div>

            </div>

        </div>

    </div>

    <div class="clr"></div>



</section>

<div class="modal-block" id="panel">

    <div class="modal-popup">

        <div class="popup-info">

            <img src="/content/HONDA/honda-logo.png" alt="Yêu cầu tư vấn" style="height: 50px;">

            <a href="tel:0919 905 293"><i class="ion-ios-telephone"></i><span>Phòng Kinh Doanh</span><b><?php echo isset($contact['hotline']) ? $contact['hotline'] : '0919 905 293'; ?></b></a>

            <ul class="desktop">

                <li>Tư vấn bán hàng chuyên nghiệp</li>

                <li>Tư vấn tài chính trả góp</li>

                <li>Đăng kí, giao xe tận nhà</li>

                <li>Trải nghiệm mua xe tuyệt vời</li>

            </ul>

            <img src="/OTO3602100212/files/san-pham/icon_crv_xanh.webp" alt="Tư vấn">

        </div>



        <form method="post" id="requestForm">

            <a href="javascript:void(0);" onclick="$('.modal-block').fadeOut();">✖</a>

            <label>Yêu cầu tư vấn, báo giá và lái thử</label>

            <p>Quý khách vui lòng để lại thông tin, Tư vấn bán hàng sẽ gọi lại ngay!</p>

            <div class="dialog-content">

                <input name="name" required placeholder="Họ tên (*)" />

                <input name="phone" required placeholder="Điện thoại (*)" />

                <input name="address" placeholder="Địa chỉ" />

                <select name="id_car" required>

                    <option value="">Chọn dòng xe quan tâm</option>

                    <option value="2">Xe Honda City</option>

                    <option value="3">Xe Honda BR-V</option>

                    <option value="4">Xe Honda Civic</option>

                    <option value="5">Xe Honda HR-V</option>

                    <option value="6">Xe Honda CR-V</option>

                    <option value="7">Xe Honda Accord</option>

                </select>

            </div>

            <button type="submit" class="btnConfirm" name="task" value="request">Gửi ngay</button>

        </form>

        <div id="responseMessage"></div>

    </div>

</div>

<script type="text/javascript">

$(document).ready(function() {

    $('#requestForm').submit(function(e) {

        e.preventDefault();



        // Disable the submit button to prevent multiple submissions

        $('#requestForm button[type="submit"]').prop('disabled', true);



        // Optional: Show a loading indicator or message

        $('#responseMessage').removeClass().text('Đang xử lý...').show();



        $.ajax({

            url: 'send_mail.php',

            type: 'POST',

            data: $(this).serialize(),

            success: function(response) {

                if (response.success) {

                    $('#responseMessage')

                        .removeClass()

                        .addClass('success-message')

                        .text('Gửi yêu cầu thành công! Cảm ơn bạn đã liên hệ.')

                        .fadeIn();



                    // Optional: Reset the form

                    $('#requestForm')[0].reset();



                    // Hide modal if applicable

                    setTimeout(function() {

                        $('#panel').fadeOut();

                    }, 4000);

                } else {

                    $('#responseMessage')

                        .removeClass()

                        .addClass('error-message')

                        .text('Có lỗi xảy ra: ' + response.error)

                        .fadeIn();

                }

            },

            error: function(xhr, status, error) {

                $('#responseMessage')

                    .removeClass()

                    .addClass('error-message')

                    .text('Có lỗi xảy ra khi kết nối với máy chủ: ' + error)

                    .fadeIn();

            },

            complete: function() {

                // Re-enable the submit button

                $('#requestForm button[type="submit"]').prop('disabled', false);

            }

        });

    });



    $('#contactForm').submit(function(e) {

        e.preventDefault();



        // Disable the submit button to prevent multiple submissions

        $('#contactForm button[type="submit"]').prop('disabled', true);



        // Optional: Show a loading indicator or message

        $('#responseMessage2').removeClass().text('Đang xử lý...').show();



        $.ajax({

            url: 'send_mail.php',

            type: 'POST',

            data: $(this).serialize(),

            success: function(response) {

                if (response.success) {

                    $('#responseMessage2')

                        .removeClass()

                        .addClass('success-message')

                        .text('Gửi yêu cầu thành công! Cảm ơn bạn đã liên hệ.')

                        .fadeIn();



                    // Optional: Reset the form

                    $('#contactForm')[0].reset();



                    // Hide modal if applicable

                    setTimeout(function() {

                        $('#panel').fadeOut();

                    }, 4000);

                } else {

                    $('#responseMessage2')

                        .removeClass()

                        .addClass('error-message')

                        .text('Có lỗi xảy ra: ' + response.error)

                        .fadeIn();

                }

            },

            error: function(xhr, status, error) {

                $('#responseMessage2')

                    .removeClass()

                    .addClass('error-message')

                    .text('Có lỗi xảy ra khi kết nối với máy chủ: ' + error)

                    .fadeIn();

            },

            complete: function() {

                // Re-enable the submit button

                $('#contactForm button[type="submit"]').prop('disabled', false);

            }

        });

    });

});





setTimeout(function() {

    $('#panel').fadeIn();

}, 1000);

</script>

<?php include 'includes/footer.php'; ?>