<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!-- SEO Meta Tags Cơ Bản -->
    <title>Honda Ôtô Cần Thơ - Đại Lý Ủy Quyền Ch<PERSON>h <PERSON>hức | Gi<PERSON>t <?php echo date('Y'); ?></title>
    <meta name="description" content="Honda Ôtô Cần Thơ - Đại lý 5S chính hãng, giá tốt nhất, khuyến mãi lớn. Civic, City, CRV, HRV và các dòng xe Honda chất lượng cao. Dịch vụ bảo dưỡng chuyên nghiệp. Hotline: 0919 905 293">
    <meta name="keywords" content="honda oto can tho, honda can tho, xe honda, oto honda, honda civic, honda city, honda crv, honda hrv, mua xe honda">

    <!-- Open Graph Tags cho Social Media -->
    <meta property="og:site_name" content="Honda Ôtô Cần Thơ">
    <meta property="og:title" content="Honda Ôtô Cần Thơ - Đại Lý Honda Chính Hãng Tại Cần Thơ">
    <meta property="og:description" content="Đại lý Honda Ôtô chính hãng tại Cần Thơ. Showroom 5S với đầy đủ các dòng xe và dịch vụ bảo hành, bảo dưỡng chuẩn Honda toàn cầu.">
    <meta property="og:image" content="/OTO3602100212/files/hondaotocantho.com.vn.jpg">
    <meta property="og:type" content="website">
    
    <!-- Các thẻ Meta bổ sung -->
    <meta name="robots" content="index, follow, max-image-preview:large, max-snippet:-1, max-video-preview:-1">
    <meta name="revisit-after" content="1 days">
    <meta name="author" content="Honda Ôtô Cần Thơ">
    <link rel="canonical" href="https://www.hondaotocantho.com.vn/">

    <!-- Favicon -->
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon.ico">
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon.ico">
    <link rel="apple-touch-icon" sizes="180x180" href="/favicon.ico">

    <!-- Schema.org Markup -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "AutoDealer",
        "name": "Honda Ôtô Cần Thơ",
        "image": "/OTO3602100212/files/hondaotocantho.com.vn.jpg",
        "description": "Đại lý Honda Ôtô chính hãng tại Cần Thơ - Chuyên cung cấp các dòng xe Honda mới nhất với giá tốt nhất thị trường",
        "@id": "https://www.hondaotocantho.com.vn",
        "url": "https://www.hondaotocantho.com.vn",
        "telephone": "0919 905 293",
        "address": {
            "@type": "PostalAddress",
            "streetAddress": "Số 7A, Võ Nguyên Giáp",
            "addressLocality": "Cần Thơ",
            "postalCode": "94000",
            "addressCountry": "VN"
        },
        "priceRange": "$$",
        "openingHoursSpecification": {
            "@type": "OpeningHoursSpecification",
            "dayOfWeek": [
                "Monday",
                "Tuesday",
                "Wednesday",
                "Thursday",
                "Friday",
                "Saturday",
                "Sunday"
            ],
            "opens": "08:00",
            "closes": "20:00"
        }
    }
    </script>
    <title>Honda Ôtô Cần Thơ</title>
    <link rel="stylesheet" href="<?php echo ASSETS_URL; ?>/css/style.css">
    <link rel="stylesheet" href="<?php echo ASSETS_URL; ?>/css/ionicons.min.css">
    <link rel="stylesheet" href="<?php echo ASSETS_URL; ?>/css/foxchat.css">
    <link href="https://fonts.googleapis.com/css?family=Roboto+Condensed:300,400,700" rel="stylesheet">
    <link rel="stylesheet" href="//code.jquery.com/ui/1.13.2/themes/base/jquery-ui.css">
    <script src="<?php echo ASSETS_URL; ?>/js/main.js"></script>
    <script src="<?php echo ASSETS_URL; ?>/js/foxchat.js"></script>
    <script src="https://code.jquery.com/ui/1.13.2/jquery-ui.js"></script>
</head>
<body>
    <?php
    if (!isset($connect) || !$connect) {
        die("Database connection failed");
    }
    // Fetch logo information
    $sql = "select * from picture where id=1;";
    $result = mysql_query($sql);
    $row = mysql_fetch_array($result);

    // Fetch contact information - with error handling
    $sql_contact = "SELECT `key`, `value` FROM config WHERE type='contact'";
    $result_contact = mysql_query($sql_contact) or die("Contact query failed: " . mysql_error());

    $contact = array();
    while ($row_contact = mysql_fetch_array($result_contact)) {
        $contact[$row_contact['key']] = $row_contact['value'];
    }
    ?>
    
    <header class="header-home background_primary">
    <div class="header-top">
        <div class="container">
            <img class="fr desktop" alt="Honda Ôtô Cần Thơ" src="<?php echo isset($rowlg['image_url']) ? $rowlg['image_url'] : '/content/HONDA/honda-logo.png'; ?>">
            <a href="./">
                <h2 class="color_primary">Honda Ôtô Cần Thơ</h2>
            </a>
            <div class="info desktop">
                <img alt="Honda Ôtô Cần Thơ" src="<?php echo ASSETS_URL; ?>/images/email.svg">
                <label>Email:</label>
                <span class="color_primary"><?php echo isset($contact['email']) ? $contact['email'] : '<EMAIL>'; ?></span>
            </div>
            <div class="info desktop" style="width: 200px;">
                <img alt="Honda Ôtô Cần Thơ" src="<?php echo ASSETS_URL; ?>/images/phone.svg">
                <label>Hotline 24/7:</label>
                <span class="color_primary"><?php echo isset($contact['hotline']) ? $contact['hotline'] : '0919 905 293'; ?></span>
            </div>
            <a class="button-menu mobile" href="javascript:void(0);" onclick="$('.menu').slideToggle();"><i class="ion-ios-keypad-outline"></i></a>
        </div>
    </div>
    <div class="clr"></div>
    <div class="container">
    <ul class="menu">
            <li><a href="./">Trang chủ</a></li>
            
            <li>
                <a href="gioi-thieu.html">Giới thiệu</a>
                <ul class="submenu">
                <?php
                $sql="select * from intro where display = 0 order by date desc;";
                $result=mysql_query($sql);
                while($row=mysql_fetch_array($result)) {
                    $url = utf8tourl(utf8convert($row['name'])) . '-' . $row['id'] . '.html';
                    echo '<li><a href="' . htmlspecialchars($url) . '">' . htmlspecialchars($row['name']) . '</a></li>';
                }
                ?>
                </ul>
            </li>

            <li>
                <a href="san-pham.html">Sản phẩm</a>
                <ul class="submenu">
                <?php
                $sql="select * from entry2 a, category b where a.category_id = b.category_id and b.type = 0 order by a.date_post desc;";
                $result=mysql_query($sql);
                while($row=mysql_fetch_array($result)) {
                    $url = 'honda-' . $row['entry_id'] . '-' . utf8tourl(utf8convert($row['title'])) . '.html';
                    echo '<li><a href="' . htmlspecialchars($url) . '">' . htmlspecialchars($row['title']) . '</a></li>';
                }
                ?>
                </ul>
            </li>

            <li>
                <a href="dich-vu.html">Dịch vụ</a>
                <ul class="submenu">
                <?php
                $sql="select * from intro where display = 1 order by date desc;";
                $result=mysql_query($sql);
                while($row=mysql_fetch_array($result)) {
                    $url = utf8tourl(utf8convert($row['name'])) . '-' . $row['id'] . '.html';
                    echo '<li><a href="' . htmlspecialchars($url) . '">' . htmlspecialchars($row['name']) . '</a></li>';
                }
                ?>
                </ul>
            </li>

            <li><a href="lai-thu.html">Lái thử</a></li>
            <li>
                <a href="tin-tuc.html">Tin tức</a>
                <ul class="submenu">
                <?php
                $sql="select * from category where type = 1 order by position asc;";
                $result=mysql_query($sql);
                while($row=mysql_fetch_array($result)) {
                    $url = 'tin-tuc-'.$row['category_id'].'-'.utf8tourl(utf8convert($row['category_name'])).'.html';
                    echo '<li><a href="' . htmlspecialchars($url) . '">' . htmlspecialchars($row['category_name']) . '</a></li>';
                }
                ?>
                </ul>
            </li>
            <li><a href="javascript:void(0);" onclick="$('#panel').fadeIn();">Liên hệ</a></li>
        </ul>
</div>
    </header>