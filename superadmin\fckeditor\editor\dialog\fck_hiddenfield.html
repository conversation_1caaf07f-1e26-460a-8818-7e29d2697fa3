<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN" >
<!--
 * FCKeditor - The text editor for Internet - http://www.fckeditor.net
 * Copyright (C) 2003-2010 <PERSON><PERSON>
 *
 * == BEGIN LICENSE ==
 *
 * Licensed under the terms of any of the following licenses at your
 * choice:
 *
 *  - GNU General Public License Version 2 or later (the "GPL")
 *    http://www.gnu.org/licenses/gpl.html
 *
 *  - GNU Lesser General Public License Version 2.1 or later (the "LGPL")
 *    http://www.gnu.org/licenses/lgpl.html
 *
 *  - Mozilla Public License Version 1.1 or later (the "MPL")
 *    http://www.mozilla.org/MPL/MPL-1.1.html
 *
 * == END LICENSE ==
 *
 * Hidden Field dialog window.
-->
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
	<title>Hidden Field Properties</title>
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
	<meta content="noindex, nofollow" name="robots" />
	<script src="common/fck_dialog_common.js" type="text/javascript"></script>
	<script type="text/javascript">

var dialog	= window.parent ;
var oEditor = dialog.InnerDialogLoaded() ;

var FCK = oEditor.FCK ;

// Gets the document DOM
var oDOM = FCK.EditorDocument ;

// Get the selected flash embed (if available).
var oFakeImage = dialog.Selection.GetSelectedElement() ;
var oActiveEl ;

if ( oFakeImage )
{
	if ( oFakeImage.tagName == 'IMG' && oFakeImage.getAttribute('_fckinputhidden') )
		oActiveEl = FCK.GetRealElement( oFakeImage ) ;
	else
		oFakeImage = null ;
}

window.onload = function()
{
	// First of all, translate the dialog box texts
	oEditor.FCKLanguageManager.TranslatePage(document) ;

	if ( oActiveEl )
	{
		GetE('txtName').value		= oActiveEl.name ;
		GetE('txtValue').value		= oActiveEl.value ;
	}

	dialog.SetOkButton( true ) ;
	dialog.SetAutoSize( true ) ;
	SelectField( 'txtName' ) ;
}


function Ok()
{
	oEditor.FCKUndo.SaveUndoStep() ;

	oActiveEl = CreateNamedElement( oEditor, oActiveEl, 'INPUT', {name: GetE('txtName').value, type: 'hidden' } ) ;

	SetAttribute( oActiveEl, 'value', GetE('txtValue').value ) ;

	if ( !oFakeImage )
	{
		oFakeImage	= oEditor.FCKDocumentProcessor_CreateFakeImage( 'FCK__InputHidden', oActiveEl ) ;
		oFakeImage.setAttribute( '_fckinputhidden', 'true', 0 ) ;

		oActiveEl.parentNode.insertBefore( oFakeImage, oActiveEl ) ;
		oActiveEl.parentNode.removeChild( oActiveEl ) ;
	}
	else
		oEditor.FCKUndo.SaveUndoStep() ;

	return true ;
}

	</script>
</head>
<body style="overflow: hidden" scroll="no">
	<table height="100%" width="100%">
		<tr>
			<td align="center">
				<table border="0" class="inhoud" cellpadding="0" cellspacing="0" width="80%">
					<tr>
						<td>
							<span fcklang="DlgHiddenName">Name</span><br />
							<input type="text" size="20" id="txtName" style="width: 100%" />
						</td>
					</tr>
					<tr>
						<td>
							<span fcklang="DlgHiddenValue">Value</span><br />
							<input type="text" size="30" id="txtValue" style="width: 100%" />
						</td>
					</tr>
				</table>
			</td>
		</tr>
	</table>
</body>
</html>
