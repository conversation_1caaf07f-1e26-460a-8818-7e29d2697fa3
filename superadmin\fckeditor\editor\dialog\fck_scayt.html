<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN" >
<!--
 * FCKeditor - The text editor for Internet - http://www.fckeditor.net
 * Copyright (C) 2003-2010 <PERSON><PERSON>
 *
 * == BEGIN LICENSE ==
 *
 * Licensed under the terms of any of the following licenses at your
 * choice:
 *
 *  - GNU General Public License Version 2 or later (the "GPL")
 *    http://www.gnu.org/licenses/gpl.html
 *
 *  - GNU Lesser General Public License Version 2.1 or later (the "LGPL")
 *    http://www.gnu.org/licenses/lgpl.html
 *
 *  - Mozilla Public License Version 1.1 or later (the "MPL")
 *    http://www.mozilla.org/MPL/MPL-1.1.html
 *
 * == END LICENSE ==
-->
<html>
	<head>
		<title>SCAYT Properties</title>
		<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
		<meta content="noindex, nofollow" name="robots">
		<script src="common/fck_dialog_common.js" type="text/javascript"></script>
		<link type="text/css" href="fck_scayt/scayt_dialog.css" rel="stylesheet" />
		<script type="text/javascript">

			var dialog	= window.parent ;
			var oEditor	= dialog.InnerDialogLoaded() ;
			var FCKLang = oEditor.FCKLang;
			var scayt = oEditor.scayt;
			var scayt_control = oEditor.scayt_control;
			var lang_list = {};
            var	sLang;
			var	fckLang;
            var chosed_lang;
			var options;
			var	tabs = scayt_control.uiTags || [1,1,0,1];
			var	userDicActive = tabs[2] == 1;
			var	captions;
			var dic_buttons = [
				// [0] contains buttons for creating
				"dic_create,dic_restore",
				// [1] contains buton for manipulation
				"dic_rename,dic_delete"
			];

			var get =
				new function(){

					var mergeObjs = function(obj1, obj2)
					{
						for (var k in obj1)
							obj2[k] = obj1[k];

						return obj2;
					};

					var removeWhitespaces = function( s )
					{
						s = s.replace( new RegExp("^ +| +$"), '' ) ;
						return s ;
					};

					var addEvent = function( el ,sEventName, fTodo )
					{
						if (el.addEventListener) {
							el.addEventListener (sEventName,fTodo,false);

						} else if (el.attachEvent) {
							el.attachEvent ("on"+sEventName,fTodo);

						} else {
							el["on"+sEventName] = fTodo;
						}
					};

					var getElementsByClassName = function (node,classname ,strTag) {
						strTag = strTag || "*";
					  	node = node || document;
						if (node.getElementsByClassName)
							return node.getElementsByClassName(classname);
						else {
							var objColl = node.getElementsByTagName(strTag);
							if (!objColl.length &&  strTag == "*" &&  node.all) objColl = node.all;
							var arr = new Array();
							var delim = classname.indexOf('|') != -1  ? '|' : ' ';
							var arrClass = classname.split(delim);
							for (var i = 0, j = objColl.length; i < j; i++) {
								var arrObjClass = objColl[i].className.split(' ');
								if (delim == ' ' && arrClass.length > arrObjClass.length) continue;
								var c = 0;
								comparisonLoop:
									for ( var k = 0, l = arrObjClass.length ; k < l ; k++ )
									{
										for ( var m = 0, n = arrClass.length ; m < n ; m++ )
										{
											if ( arrClass[m] == arrObjClass[k] )
												c++ ;

											if ( ( delim == '|' && c == 1 ) || ( delim == ' ' && c == arrClass.length ) )
											{
												arr.push( objColl[i] ) ;
												break comparisonLoop ;
											}
										}
									}
							}
							return arr;
						}
					};

					var hasClassName = function ( sClassName, elem ) {
						//.split(/\s+/);
						var aCnames = elem.className.split(/\s+/) || [];
						for (var i=0, l=aCnames.length; i<l ; i++){
							if (sClassName == aCnames[i])
								return true;
						}
						return false;
					}

					var single = {
						addClass 	: function ( sClassName ) {
							//console.info( sClassName, this.className, );
							if ( hasClassName(sClassName , this) )
								return this;
							var s = removeWhitespaces(this.className + " " +sClassName);
							this.className = s;
							return this;

						},
						removeClass : function ( sClassName ) {
							var s = removeWhitespaces(this.className.replace(sClassName,""));
							this.className = s;
							return this;
						},
						setStyle : function( oStyles )
						{
							for ( var style in oStyles )
							{
								this.style[style] = oStyles[style] ;
							}
							return this ;
						},
						bindOnclick		: function ( handler ) {
							//addEvent( this, "click" , handler);
							this.onclick = handler;
							return this;
						},
						bindOnchange	: function ( handler ) {
							//addEvent( this, "change" , handler);
							this.onchange = handler;
							return this;
						},
						getAttr : function ( sAttrName )
						{
							if ( !sAttrName )
								return null;

							return this[sAttrName];
						},
						setAttr : function ( sAttrName , attrVal )
						{
							if ( !sAttrName || !attrVal )
								return null;

							this[sAttrName] = attrVal;

							return this;
						},
						remAttr : function ( sAttrName )
						{
							if ( !sAttrName )
								return null;
						}
					};

					var singleCaller = function ( sMethod,args ) {
						for ( var i=0, l=this.length; i<l ; i++ ){
							var oItem = mergeObjs( single, this[i] );
							oItem[sMethod].apply(this[i],args);
						}
					};


					var collection = {

						addClass 	: function ( sClassName ){
							singleCaller.call(this, "addClass", [sClassName])
							return this;
						},
						removeClass 	: function ( sClassName ) {
							singleCaller.call(this, "removeClass", [sClassName])
							return this;
						},
						setStyle		: function ( oStyles ) {
							singleCaller.call(this, "setStyle", [oStyles])
							return this;
						},
						bindOnclick		: function ( f ) {
							singleCaller.call(this, "bindOnclick", [f])
							return this;
						},
						bindOnchange	: function ( f ) {
							singleCaller.call(this, "bindOnchange", [f])
							return this;
						},

						forEach : function ( fTodo ) {
							//el,i
							for (var i=0, l=this.length; i<l ; i++){
								fTodo.apply(this[i], [this[i],i ]);
							}
							return this;
						}

					};



					this.byClass = function( sClassName ){
						var o = getElementsByClassName(document, sClassName );
						return o ? mergeObjs( collection, o ) : o;
					};

					this.byId = function( sId ){
						var o = document.getElementById( sId );
						return o ? mergeObjs( single, o ) : o;
					};

					this.gup = function ( name ){
				        name = name.replace( /[\[]/, '\\\[' ).replace( /[\]]/, '\\\]' ) ;
				        var regexS = '[\\?&]' + name + '=([^&#]*)' ;
				        var regex = new RegExp( regexS ) ;
				        var results = regex.exec( window.location.href ) ;

				        if( results == null )
				            return '' ;
				        else
				            return results[ 1 ] ;
					};
					this.wrap = function ( o ) {
						return o ? mergeObjs( single, o ) : o;
					};
					this.forEach = function ( oScope, fTodo ){
						collection.forEach.apply( oScope,[fTodo] );
					};

				 };



			// Add the dialog tabs.
			tabs[0] == 1 && dialog.AddTab( 'options', 'Options' ) ;
			tabs[1] == 1 && dialog.AddTab( 'langs', 'Languages' ) ;
			tabs[2] == 1 && dialog.AddTab( 'dictionary', 'Dictionary' ) ;
			tabs[3] == 1 && dialog.AddTab( 'about', 'About' ) ;

			// Function called when a dialog tab is selected.
			function OnDialogTabChange( tabCode )
			{
				ShowE('inner_options'	, ( tabCode == 'options' ) ) ;
				ShowE('inner_langs'	, ( tabCode == 'langs' ) ) ;
				ShowE('inner_dictionary'		, ( tabCode == 'dictionary' ) ) ;
				ShowE('inner_about'	, ( tabCode == 'about' ) ) ;
			}





			window.onload = function()
			{
				// Things to do when the page is loaded.

				if ( document.location.search.length )
					dialog.SetSelectedTab( document.location.search.substr(1) ) ;

				dialog.SetOkButton( true ) ;


	                if (!scayt) throw "SCAYT is undefined";
	                if (!scayt_control) throw "SCAYT_CONTROL is undefined";

					// show alowed tabs
					tabs = scayt_control.uiTags || [1,1,1,0];


					sLang = scayt_control.getLang();
	                fckLang = "en";
					options = scayt_control.option();
					// apply captions
					scayt.getCaption( fckLang, function( caps )
					{
						//console.info( "scayt.getCaption runned" )
						captions = caps;
						apllyCaptions();
						//lang_list = scayt.getLangList();
		                lang_list = scayt.getLangList() ;//|| {ltr: {"en_US" : "English","en_GB" : "British English","pt_BR" : "Brazilian Portuguese","da_DK" : "Danish","nl_NL" : "Dutch","en_CA" : "English Canadian","fi_FI" : "Finnish","fr_FR" : "French","fr_CA" : "French Canadian","de_DE" : "German","el_GR" : "Greek","hu_HU" : "Hungarian","it_IT" : "Italian","nb_NO" : "Norwegian","pl_PL" : "Polish","pt_PT" : "Portuguese","ru_RU" : "Russian","es_ES" : "Spanish","sv_SE" : "Swedish","tr_TR" : "Turkish","uk_UA" : "Ukrainian","cy_GB" : "Welsh"},rtl: {"ar_EG" : "Arabic"}};




		                // ** animate options
		                get.byClass("_scayt_option").forEach(function(el,i){

							if ('undefined' != typeof(options[el.name])) {
		                        // *** set default values

		                        if ( 1 == options[ el.name ] ){
		                           //el.setAttribute("checked","true");
								   get.wrap(el).setAttr("checked" ,true)
								   //document.all_options[el.name].checked = "true";
								   //el.checked = true;
								   //alert( options[ dojo.attr(el ,'name') ] + " el " )
		                        }
								//console.info(options)
		                        // *** bind events
		                        get.wrap(el).bindOnclick( function(ev){

									var that = get.wrap(this);
									var isCheck = that.getAttr("checked");
									//console.info(isCheck)
		                            if ( isCheck == false ) {

										//that.setAttr("checked",false);
										options[ this.name ] = 0;
		                            }else{
		                                //that.setAttr("checked",true);
										options[ this.name ] = 1;
		                            }
									//console.info(options)
		                        });
		                    }
		                });


		                // * Create languages tab
		                // ** convert langs obj to array
		                var lang_arr = [];

		                for (var k in lang_list.rtl){
		                    // find curent lang
		                    if ( k == sLang)
		                        chosed_lang = lang_list.rtl[k] + "::" + k;
		                    lang_arr[lang_arr.length] = lang_list.rtl[k] + "::" + k;

		                }
		                for (var k in lang_list.ltr){
		                     // find curent lang
		                     if ( k == sLang)
		                        chosed_lang = lang_list.ltr[k] + "::" + k;
		                    lang_arr[lang_arr.length] = lang_list.ltr[k] + "::" + k;
		                }
		                lang_arr.sort();

		                // ** find lang containers

		                var lcol = get.byId("lcolid");
		                var rcol = get.byId("rcolid");
		                // ** place langs in DOM

		                get.forEach(lang_arr , function( l , i ){

							//console.info( l,i );

							var l_arr = l.split('::');
		                    var l_name = l_arr[0];
		                    var l_code = l_arr[1];
		                    var row = document.createElement('div');
		                    row.id = l_code;
		                    row.className = "li";
		                    // split langs on half
		                    var col = ( i < lang_arr.length/2 ) ? lcol:rcol ;

		                    // append row
		                    //console.dir( col )
		                    col.appendChild(row);
		                    var row_dom = get.byId( l_code )
		                    row_dom.innerHTML = l_name;

		                    var checkActiveLang = function( id ){
		                        return chosed_lang.split("::")[1] == id;
		                    };
		                    // bind click
		                    row_dom.bindOnclick(function(ev){

		                        if ( checkActiveLang(this.id) ) return false;
		                        var elId = this.id;
								get.byId(this.id)
	                            	.addClass("Button")
	                            	.removeClass("DarkBackground");

		                        window.setTimeout( function (){ get.byId(elId).setStyle({opacity:"0.5",cursor:"no-drop"});  } ,300 );

		                        get.byId(chosed_lang.split("::")[1])
		                            .addClass("DarkBackground")
		                            .removeClass("Button")
		                            .setStyle({opacity:"1",cursor:"pointer"});

		                        chosed_lang = this.innerHTML + "::" + this.id;
		                        return true;
		                    })
							.setStyle({
		                        cursor:"pointer"
		                    });
		                    // select current lang
		                    if (l == chosed_lang)
		                        row_dom.addClass("Button").setStyle({opacity:"0.5",cursor:"no-drop"});
		                    else
		                        row_dom.addClass("DarkBackground").setStyle({opacity:"1"});

		                });
						// * user dictionary
						if ( userDicActive ){
							initUserDictionary()

						}
					});



			}




			var buttons = [ 'dic_create','dic_delete','dic_rename','dic_restore' ];
			var labels  = [ 'mixedCase','mixedWithDigits','allCaps','ignoreDomainNames' ];


			function apllyCaptions ( )
			{

				// fill tabs headers
				// add missing captions

				get.byClass("PopupTab").forEach(function(el,i){

					if ( tabs[i] == 1 ){
						el.style.display = "block";
					}
					el.innerHTML = captions['tab_'+el.id];

				});

				// Fill options labels.
				for ( i in labels )
				{
					var label = 'label_' + labels[ i ],
						labelElement = document.getElementById( label );

					if (  'undefined' != typeof labelElement
					   && 'undefined' != typeof captions[ label ] && captions[ label ] !== ""
					   && 'undefined' != typeof options[labels[ i ]] )
					{
						labelElement.innerHTML = captions[ label ];
						var labelParent = labelElement.parentNode;
						labelParent.style.display = "block";
					}
				}
				// fill dictionary section
				for ( var i in buttons )
				{
					var button = buttons[ i ];
					get.byId( button ).innerHTML = '<span>' + captions[ 'button_' + button]  +'</span>' ;
				}
				get.byId("dname").innerHTML = captions['label_dname'];
				get.byId( 'dic_info' ).innerHTML = captions[ 'dic_info' ];

				// fill about tab
				var about = '<p>' + captions[ 'about_throwt_image' ] + '</p>'+
					'<p>' + captions[ 'version' ]  + scayt.version.toString() + '</p>' +
					'<p>' + captions[ 'about_throwt_copy' ] + '</p>';

				get.byId( 'scayt_about' ).innerHTML = about;

			}


			function initUserDictionary () {

				scayt.getNameUserDictionary(
					function( o )
					{
						var dic_name = o.dname;
						if ( dic_name )
						{
							get.byId( 'dic_name' ).value = dic_name;
							display_dic_buttons( dic_buttons[1] );
						}
						else
							display_dic_buttons( dic_buttons[0] );

					},
					function ()
					{
						get.byId( 'dic_name' ).value("");
						dic_error_message(captions["err_dic_enable"] || "Used dictionary are unaveilable now.")
					}
				);

				dic_success_message("");

				 // ** bind event listeners
                get.byClass("button").bindOnclick(function( ){

					// get dic name
					var dic_name = get.byId('dic_name').value ;
					// check common dictionary rules
					if (!dic_name) {
						dic_error_message(" Dictionary name should not be empty. ");
						return false;
					}
					//apply handler
					window[this.id].apply( window, [this, dic_name, dic_buttons ] );

					//console.info( typeof window[this.id], window[this.id].calle )
					return false;
				});

			}

			dic_create = function( el, dic_name , dic_buttons )
			{
				// comma separated button's ids include repeats if exists
				var all_buttons = dic_buttons[0] + ',' + dic_buttons[1];

				var err_massage = captions["err_dic_create"];
				var suc_massage = captions["succ_dic_create"];
				//console.info("--plugin ");

				scayt.createUserDictionary(dic_name,
					function(arg)
						{
							//console.info( "dic_create callback called with args" , arg );
							hide_dic_buttons ( all_buttons );
							display_dic_buttons ( dic_buttons[1] );
							suc_massage = suc_massage.replace("%s" , arg.dname );
							dic_success_message (suc_massage);
						},
					function(arg)
						{
							//console.info( "dic_create errorback called with args" , arg )
							err_massage = err_massage.replace("%s" ,arg.dname );
							dic_error_message ( err_massage + "( "+ (arg.message || "") +")");
						});

			};

			dic_rename = function( el, dic_name , dic_buttons )
			{
				//
				// try to rename dictionary
				// @TODO: rename dict
				//console.info ( captions["err_dic_rename"] )
				var err_massage = captions["err_dic_rename"] || "";
				var suc_massage = captions["succ_dic_rename"] || "";
				scayt.renameUserDictionary(dic_name,
					function(arg)
						{
							//console.info( "dic_rename callback called with args" , arg );
							suc_massage = suc_massage.replace("%s" , arg.dname );
							set_dic_name( dic_name );
							dic_success_message ( suc_massage );
						},
					function(arg)
						{
							//console.info( "dic_rename errorback called with args" , arg )
							err_massage = err_massage.replace("%s" , arg.dname  );
							set_dic_name( dic_name );
							dic_error_message( err_massage + "( " + ( arg.message || "" ) + " )" );
						});
			};

			dic_delete = function ( el, dic_name , dic_buttons )
			{
				var all_buttons = dic_buttons[0] + ',' + dic_buttons[1];
				var err_massage = captions["err_dic_delete"];
				var suc_massage = captions["succ_dic_delete"];

				// try to delete dictionary
				// @TODO: delete dict
				scayt.deleteUserDictionary(
					function(arg)
						{
							//console.info( "dic_delete callback " , dic_name ,arg );
							suc_massage = suc_massage.replace("%s" , arg.dname );
							hide_dic_buttons ( all_buttons );
							display_dic_buttons ( dic_buttons[0] );
							set_dic_name( "" ); // empty input field
							dic_success_message( suc_massage );
						},
					function(arg)
						{
							//console.info( " dic_delete errorback called with args" , arg )
							err_massage = err_massage.replace("%s" , arg.dname );
							dic_error_message(err_massage);
						});
			};

			dic_restore = dialog.dic_restore || function ( el, dic_name , dic_buttons )
			{
				// try to restore existing dictionary
				var all_buttons = dic_buttons[0] + ',' + dic_buttons[1];
				var err_massage = captions["err_dic_restore"];
				var suc_massage = captions["succ_dic_restore"];

				scayt.restoreUserDictionary(dic_name,
					function(arg)
						{
							//console.info( "dic_restore callback called with args" , arg );
							suc_massage = suc_massage.replace("%s" , arg.dname );
							hide_dic_buttons ( all_buttons );
							display_dic_buttons(dic_buttons[1]);
							dic_success_message( suc_massage );
						},
					function(arg)
						{
							//console.info( " dic_restore errorback called with args" , arg )
							err_massage = err_massage.replace("%s" , arg.dname );
							dic_error_message( err_massage );
						});
			};

			function dic_error_message( m )
			{
				if ( !m )
					return ;

				get.byId('dic_message').innerHTML =  '<span class="error">' + m + '</span>' ;
			}

            function dic_success_message( m )
            {
				if ( !m )
					return ;

				get.byId('dic_message').innerHTML = '<span class="success">' + m + '</span>' ;
			}

			function display_dic_buttons ( sIds ){
				sIds = new String( sIds );
				get.forEach( sIds.split(','), function ( id,i) {
					get.byId(id).setStyle({display:"inline"});
				});
			}
			function hide_dic_buttons ( sIds ){
				sIds = new String( sIds );
				get.forEach( sIds.split(','), function ( id,i) {
					get.byId(id).setStyle({display:"none"});
				});
			}
			function set_dic_name ( dic_name ) {
				get.byId('dic_name').value = dic_name;
			}
			function display_dic_tab () {
				get.byId("dic_tab").style.display = "block";
			}

			function Ok()
			{
				// Things to do when the Ok button is clicked.
				var c = 0;
			    // set upp options if any was set
			    var o = scayt_control.option();
				//console.info(options)
			    for ( var oN in options ) {

			        if ( o[oN] != options[oN] && c == 0){
						//console.info( "set option " )
			            scayt_control.option( options );
			            c++;
			        }
			    }
			    //setup languge if it was change
			    var csLang = chosed_lang.split("::")[1];
			    if ( csLang && sLang != csLang ){
			        scayt_control.setLang( csLang );
					//console.info(sLang+" -> "+csLang , scayt_control)
			        c++;
			    }

			    if ( c > 0 )  scayt_control.refresh();

			    return dialog.Cancel();

			}

		</script>
	</head>
	<body style="OVERFLOW: hidden" scroll="no">
		<div class="tab_container" id="inner_options">

           <ul id="scayt_options">
               <li class="_scayt_options">
                   <input class="_scayt_option" type="checkbox" value="0" name="allCaps" />
                   <label for="allCaps" id="label_allCaps"></label>
               </li>
               <li>
                   <input class="_scayt_option" type="checkbox" value="0" name="ignoreDomainNames" />
                   <label for="ignoreDomainNames" id="label_ignoreDomainNames"></label>
               </li>
               <li>
                   <input class="_scayt_option" type="checkbox" value="0" name="mixedCase" />
                   <label for="mixedCase" id="label_mixedCase"></label>
               </li>
               <li>
                   <input class="_scayt_option" type="checkbox" value="0" name="mixedWithDigits" />
                   <label for="mixedWithDigits" id="label_mixedWithDigits"></label>
               </li>
           </ul>
		</div>
		<div class="tab_container" id="inner_langs">

		   <div class="lcol" id="lcolid"></div>
           <div class="rcol" id="rcolid"></div>
		</div>
		<div class="tab_container" id="inner_dictionary">

		   <div id="dic_message"></div>
			<div id="_off_dic_tab" class="dictionary" >
				<div style="padding-left:10px;">
							<label id="dname" for="dname"></label>
							<input type="text" size="14" maxlength="15" value="" id="dic_name" name="dic_name"/>
						</div>
						<div class="dic_buttons">
							<a href="#" id="dic_create" class="button">  </a>
							<a href="#" id="dic_delete" class="button">  </a>
							<a href="#" id="dic_rename" class="button">  </a>
							<a href="#" id="dic_restore" class="button">  </a>
						</div>

						<div id="dic_info"></div>

			</div>
		</div>
		<div id="inner_about" class="tab_container">
		   <div id="scayt_about"></div>
		</div>
	</body>
</html>
