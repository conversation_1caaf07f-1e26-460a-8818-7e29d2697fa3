<public:component lightweight="true">

<public:attach event="oncontentready" onevent="ShowBorders()" />
<public:attach event="onpropertychange" onevent="OnPropertyChange()" />

<script language="javascript">

var oClassRegex = /\s*FCK__ShowTableBorders/ ;

function ShowBorders()
{
	if ( this.border == 0 )
	{
		if ( !oClassRegex.test( this.className ) )
			this.className += ' FCK__ShowTableBorders' ;
	}
	else
	{
		if ( oClassRegex.test( this.className ) )
		{
			this.className = this.className.replace( oClassRegex, '' ) ;
			if ( this.className.length == 0 )
				this.removeAttribute( 'className', 0 ) ;
		}
	}
}

function OnPropertyChange()
{
	if ( event.propertyName == 'border' || event.propertyName == 'className' )
		ShowBorders.call(this) ;
}

</script>

</public:component>
