<div class="widget-btn-list">
    <a href="https://m.me/loanmoon130419" rel="nofollow" class="facebook" target="_blank"></a>
    <a href="https://zalo.me/0919905293" class="zalo" rel="nofollow" target="_blank"></a>
    <a href="javascript:void(0);" onclick="$('#panel').fadeIn();" class="contact" rel="nofollow"></a>
</div>
<div class="echbay-alo-phone phonering-alo-phone phonering-alo-green style-for-position-bl">
    <a onclick="return gtag_report_conversion('tel:0919 905 293');" href="tel:0919 905 293"><label>0919 905 293</label>
    </a>
    <div class="phonering-alo-ph-circle-fill"></div>
    <div class="phonering-alo-ph-img-circle"><a onclick="return gtag_report_conversion('tel:0919 905 293');"
            href="tel:0919 905 293" class="">.</a></div>
</div>
<div class="ft-svg-container"><svg viewBox="0 0 202.9 45.5">
        <clipPath id="menu" clipPathUnits="objectBoundingBox" transform="scale(0.0049285362247413 0.021978021978022)">
            <path d="M6.7,45.5c5.7,0.1,14.1-0.4,23.3-4c5.7-2.3,9.9-5,18.1-10.5c10.7-7.1,11.8-9.2,20.6-14.3c5-2.9,9.2-5.2,15.2-7
			  c7.1-2.1,13.3-2.3,17.6-2.1c4.2-0.2,10.5,0.1,17.6,2.1c6.1,1.8,10.2,4.1,15.2,7c8.8,5,9.9,7.1,20.6,14.3c8.3,5.5,12.4,8.2,18.1,10.5
			  c9.2,3.6,17.6,4.2,23.3,4H6.7z" />
        </clipPath>
    </svg></div>
<style>
:root {
    --navicolor: #ffffff !important;
    --backcolor: #e10e2e !important;
}

.ft-navi-chaton svg {
    border-radius: 50px;
}
</style>
<div class="ft-boxnavi navi-skin1">
    <div class="ft-navi">
        <div><a rel="nofollow" title="Trang chủ" href="/"><svg width="24" height="24" viewBox="0 0 24 24" fill="none"
                    xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" clip-rule="evenodd"
                        d="M12.45 4.90342C12.1833 4.70342 11.8167 4.70342 11.55 4.90342L5.05 9.77842C4.86115 9.92006 4.75 10.1423 4.75 10.3784V18.4998C4.75 18.9141 5.08579 19.2498 5.5 19.2498H9V16.9998C9 15.343 10.3431 13.9998 12 13.9998C13.6569 13.9998 15 15.343 15 16.9998V19.2498H18.5C18.9142 19.2498 19.25 18.9141 19.25 18.4998V10.3784C19.25 10.1423 19.1389 9.92006 18.95 9.77842L12.45 4.90342ZM10.65 3.70342C11.45 3.10342 12.55 3.10342 13.35 3.70342L19.85 8.57842C20.4166 9.00334 20.75 9.67021 20.75 10.3784V18.4998C20.75 19.7425 19.7426 20.7498 18.5 20.7498H14.25C13.8358 20.7498 13.5 20.4141 13.5 19.9998V16.9998C13.5 16.1714 12.8284 15.4998 12 15.4998C11.1716 15.4998 10.5 16.1714 10.5 16.9998V19.9998C10.5 20.4141 10.1642 20.7498 9.75 20.7498H5.5C4.25736 20.7498 3.25 19.7425 3.25 18.4998V10.3784C3.25 9.67021 3.58344 9.00334 4.15 8.57842L10.65 3.70342Z"
                        fill="#323544"></path>
                </svg>
                <span class="ft-navi-span">Trang chủ</span></a></div>
        <div><a rel="nofollow" title="Sản phẩm" href="/san-pham.html"><svg width="24" height="24" viewBox="0 0 24 24"
                    fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" clip-rule="evenodd"
                        d="M7.69961 3.25C6.76139 3.25 5.92165 3.83218 5.59256 4.71079L4.64148 7.25L2.75 7.25C2.33579 7.25 2 7.58579 2 8C2 8.41421 2.33579 8.75 2.75 8.75H4.07964L4.03637 8.86552C3.14353 9.1639 2.5 10.0068 2.5 11V18C2.5 18.8284 3.17157 19.5 4 19.5C4.82843 19.5 5.5 18.8284 5.5 18V17H18.5V18C18.5 18.8284 19.1716 19.5 20 19.5C20.8284 19.5 21.5 18.8284 21.5 18V11C21.5 10.0073 20.8571 9.16474 19.965 8.86597L19.9215 8.75H21.25C21.6642 8.75 22 8.41421 22 8C22 7.58579 21.6642 7.25 21.25 7.25H19.3597L18.4086 4.71079C18.0795 3.83218 17.2398 3.25 16.3016 3.25H7.69961ZM20 11C20 10.5858 19.6642 10.25 19.25 10.25H4.75C4.33579 10.25 4 10.5858 4 11V15.5H7.25V14C7.25 12.7574 8.25736 11.75 9.5 11.75H14.5C15.7426 11.75 16.75 12.7574 16.75 14V15.5H20V11ZM18.3198 8.75L17.0039 5.23693C16.8942 4.94406 16.6143 4.75 16.3016 4.75H7.69961C7.38687 4.75 7.10695 4.94406 6.99726 5.23693L5.68141 8.75H18.3198ZM15.25 15.5H8.75V14C8.75 13.5858 9.08579 13.25 9.5 13.25H14.5C14.9142 13.25 15.25 13.5858 15.25 14V15.5Z"
                        fill="#323544"></path>
                </svg>
                <span class="ft-navi-span">Sản phẩm</span></a></div>
        <div class="ft-navi-cen-momo">
            <div class="ft-navi-cen-but ft-navi-cen-but-momo"><a rel="nofollow" title="Tư vấn"
                    href="tel:0919905293"><i><svg width="24" height="24" viewBox="0 0 24 24" fill="none"
                            xmlns="http://www.w3.org/2000/svg">
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                d="M4.26534 3.25728C5.54121 1.98143 7.69866 2.37266 8.4453 4.01527L10.0666 7.58207C10.4662 8.46115 10.3393 9.47941 9.7603 10.2298C9.57069 10.4755 9.33228 10.6266 9.14342 10.7234L6.34328 12.1589C7.02276 13.246 7.83521 14.2739 8.78072 15.2194C9.72626 16.1649 10.7541 16.9774 11.8412 17.6569L13.2768 14.8567C13.3736 14.6679 13.5247 14.4295 13.7704 14.2399C14.5208 13.6608 15.539 13.534 16.4181 13.9336L19.9849 15.5549C21.6275 16.3015 22.0187 18.459 20.7429 19.7348L19.6747 20.803C19.1484 21.3293 18.3886 21.603 17.6192 21.4643C13.993 20.8106 10.5219 19.0819 7.72006 16.28C4.91826 13.4782 3.18949 10.0071 2.53579 6.38098C2.39708 5.61151 2.6708 4.85172 3.19715 4.32541C3.19715 4.32541 3.19715 4.32541 3.19715 4.32541M4.26534 3.25728C4.26533 3.25729 4.26534 3.25728 4.26534 3.25728V3.25728ZM13.148 18.3959C14.656 19.1633 16.2534 19.694 17.8853 19.9881C18.1347 20.0331 18.4082 19.9481 18.614 19.7424L19.6822 18.6742C20.2175 18.1389 20.0534 17.2337 19.3642 16.9204L15.7974 15.2991C15.4288 15.1316 15.0014 15.1846 14.6867 15.4274C14.6868 15.4274 14.6869 15.4273 14.6867 15.4274C14.6855 15.4284 14.6781 15.4349 14.6657 15.4513C14.6513 15.4704 14.6332 15.499 14.6116 15.5411L13.148 18.3959ZM5.60427 10.8522L8.4591 9.38858C8.50118 9.367 8.52978 9.34884 8.54888 9.33445C8.56744 9.32046 8.57309 9.31298 8.57276 9.31341C8.81555 8.99879 8.86856 8.57137 8.70102 8.20278L7.07975 4.63597C6.76648 3.94679 5.86129 3.78265 5.32598 4.31796L4.25778 5.3861C4.05198 5.59188 3.96702 5.86538 4.01199 6.11487C4.30617 7.74671 4.83685 9.34411 5.60427 10.8522Z"
                                fill="#323544"></path>
                            <path d="M3.19715 4.32541L4.26534 3.25728L3.19715 4.32541Z" fill="#323544"></path>
                        </svg></i><br><span class="ft-navi-cen-span">Tư vấn</span></a></div>
        </div>
        <div><a rel="nofollow" title="Lái thử" href="/lai-thu.html"><svg version="1.1" id="Capa_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
	 viewBox="0 0 480 480" style="enable-background:new 0 0 480 480;" xml:space="preserve">
<g>
	<g>
		<path d="M240,216c-26.469,0-48,21.531-48,48s21.531,48,48,48s48-21.531,48-48S266.469,216,240,216z M240,296
			c-17.645,0-32-14.352-32-32s14.355-32,32-32s32,14.352,32,32S257.645,296,240,296z"/>
	</g>
</g>
<g>
	<g>
		<path d="M240,0C107.664,0,0,107.664,0,240s107.664,240,240,240s240-107.664,240-240S372.336,0,240,0z M240,464
			C116.484,464,16,363.516,16,240S116.484,16,240,16s224,100.484,224,224S363.516,464,240,464z"/>
	</g>
</g>
<g>
	<g>
		<path d="M240,48C134.133,48,48,134.133,48,240s86.133,192,192,192s192-86.133,192-192S345.867,48,240,48z M240,64
			c74.785,0,138.781,46.905,164.228,112.825l-59.938,19.979c-20.559,6.852-42.785,5.781-62.602-3.031
			c-26.492-11.781-56.887-11.781-83.371,0l-3.031,1.344c-18.016,8.016-38.441,9.633-57.512,4.547L73.672,182.57
			C97.54,113.646,163.061,64,240,64z M64,240c0-1.63,0.079-3.241,0.124-4.86l5.556,1.852
			c78.876,26.291,134.657,95.203,144.579,177.107C129.373,401.603,64,328.302,64,240z M240,416c-3.177,0-6.332-0.095-9.468-0.262
			c-9.454-89.602-69.874-165.284-155.79-193.918l-9.406-3.135c0.857-7.06,2.118-13.995,3.788-20.774l64.524,17.207
			c8.648,2.305,17.527,3.453,26.398,3.453c14.297,0,28.559-2.969,41.734-8.828l3.031-1.344c22.359-9.953,48.008-9.945,70.398,0.008
			c23.457,10.414,49.785,11.695,74.141,3.57l59.939-19.98c2.456,8.647,4.265,17.559,5.373,26.688l-9.406,3.135
			c-85.917,28.634-146.336,104.316-155.79,193.918C246.332,415.905,243.177,416,240,416z M265.742,414.099
			c9.922-81.904,65.702-150.816,144.579-177.107l5.556-1.852c0.044,1.619,0.124,3.23,0.124,4.86
			C416,328.302,350.627,401.603,265.742,414.099z"/>
	</g>
</g>
<g>
</g>
<g>
</g>
<g>
</g>
<g>
</g>
<g>
</g>
<g>
</g>
<g>
</g>
<g>
</g>
<g>
</g>
<g>
</g>
<g>
</g>
<g>
</g>
<g>
</g>
<g>
</g>
<g>
</g>
</svg>
                <span class="ft-navi-span">Lái thử</span></a></div>
        <div><a rel="nofollow" title="Dự toán" href="javascript:void(0);" onclick="$('#panel').fadeIn();"><svg width="24" height="24" viewBox="0 0 24 24"
                    fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                        d="M7.5 5.75C7.5 5.33579 7.83579 5 8.25 5H15.75C16.1642 5 16.5 5.33579 16.5 5.75V7.25C16.5 7.66421 16.1642 8 15.75 8H8.25C7.83579 8 7.5 7.66421 7.5 7.25V5.75Z"
                        fill="#343C54"></path>
                    <path
                        d="M8.5 9.85C8.00294 9.85 7.6 10.2529 7.6 10.75C7.6 11.2471 8.00294 11.65 8.5 11.65C8.99706 11.65 9.4001 11.2471 9.4001 10.75C9.4001 10.2529 8.99706 9.85 8.5 9.85Z"
                        fill="#343C54"></path>
                    <path
                        d="M11.1 10.75C11.1 10.2529 11.5029 9.85 12 9.85C12.4971 9.85 12.9001 10.2529 12.9001 10.75C12.9001 11.2471 12.4972 11.65 12.0001 11.65C11.503 11.65 11.1 11.2471 11.1 10.75Z"
                        fill="#343C54"></path>
                    <path
                        d="M15.5 9.85C15.0029 9.85 14.6 10.2529 14.6 10.75C14.6 11.2471 15.0029 11.65 15.5 11.65C15.9971 11.65 16.4001 11.2471 16.4001 10.75C16.4001 10.2529 15.9971 9.85 15.5 9.85Z"
                        fill="#343C54"></path>
                    <path
                        d="M7.6 14.25C7.6 13.7529 8.00294 13.35 8.5 13.35C8.99706 13.35 9.4001 13.7529 9.4001 14.25C9.4001 14.7471 8.99716 15.15 8.5001 15.15C8.00304 15.15 7.6 14.7471 7.6 14.25Z"
                        fill="#343C54"></path>
                    <path
                        d="M12 13.35C11.5029 13.35 11.1 13.7529 11.1 14.25C11.1 14.7471 11.5029 15.15 12 15.15C12.4971 15.15 12.9001 14.7471 12.9001 14.25C12.9001 13.7529 12.4971 13.35 12 13.35Z"
                        fill="#343C54"></path>
                    <path
                        d="M14.6 14.25C14.6 13.7529 15.0029 13.35 15.5 13.35C15.9971 13.35 16.4001 13.7529 16.4001 14.25C16.4001 14.7471 15.9972 15.15 15.5001 15.15C15.003 15.15 14.6 14.7471 14.6 14.25Z"
                        fill="#343C54"></path>
                    <path
                        d="M8.5 16.85C8.00294 16.85 7.6 17.2529 7.6 17.75C7.6 18.2471 8.00294 18.65 8.5 18.65C8.99706 18.65 9.4001 18.2471 9.4001 17.75C9.4001 17.2529 8.99706 16.85 8.5 16.85Z"
                        fill="#343C54"></path>
                    <path
                        d="M11.1 17.75C11.1 17.2529 11.5029 16.85 12 16.85C12.4971 16.85 12.9001 17.2529 12.9001 17.75C12.9001 18.2471 12.4972 18.65 12.0001 18.65C11.503 18.65 11.1 18.2471 11.1 17.75Z"
                        fill="#343C54"></path>
                    <path
                        d="M15.5 16.85C15.0029 16.85 14.6 17.2529 14.6 17.75C14.6 18.2471 15.0029 18.65 15.5 18.65C15.9971 18.65 16.4001 18.2471 16.4001 17.75C16.4001 17.2529 15.9971 16.85 15.5 16.85Z"
                        fill="#343C54"></path>
                    <path fill-rule="evenodd" clip-rule="evenodd"
                        d="M4.5 4.25C4.5 3.00736 5.50736 2 6.75 2H17.25C18.4926 2 19.5 3.00736 19.5 4.25V19.75C19.5 20.9926 18.4926 22 17.25 22H6.75C5.50736 22 4.5 20.9926 4.5 19.75V4.25ZM6.75 3.5C6.33579 3.5 6 3.83579 6 4.25V19.75C6 20.1642 6.33579 20.5 6.75 20.5H17.25C17.6642 20.5 18 20.1642 18 19.75V4.25C18 3.83579 17.6642 3.5 17.25 3.5H6.75Z"
                        fill="#343C54"></path>
                </svg>
                <span class="ft-navi-span">Dự toán</span></a></div>
    </div>
    <div class="ft-menu-border"></div>
</div>
<footer>
    <?php
    if (!isset($connect) || !$connect) {
        die("Database connection failed");
    }
    // Fetch logo information
    $sql = "select * from picture where id=1;";
    $result = mysql_query($sql);
    $row = mysql_fetch_array($result);

    // Fetch contact information - with error handling
    $sql_contact = "SELECT `key`, `value` FROM config WHERE type='contact'";
    $result_contact = mysql_query($sql_contact) or die("Contact query failed: " . mysql_error());

    $contact = array();
    while ($row_contact = mysql_fetch_array($result_contact)) {
        $contact[$row_contact['key']] = $row_contact['value'];
    }
    ?>
    <div class="container">
        <div class="row">
            <div class="col col-4">
                <h2>Honda &Ocirc;t&ocirc; Cần Thơ</h2>
                <ul>
                    <li><svg version="1.1" id="Capa_1" xmlns="http://www.w3.org/2000/svg"
                            xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 477 477"
                            style="enable-background:new 0 0 477 477;" xml:space="preserve">
                            <g>
                                <g>
                                    <path
                                        d="M238.4,0C133,0,47.2,85.8,47.2,191.2c0,12,1.1,24.1,3.4,35.9c0.1,0.7,0.5,2.8,1.3,6.4c2.9,12.9,7.2,25.6,12.8,37.7 c20.6,48.5,65.9,123,165.3,202.8c2.5,2,5.5,3,8.5,3s6-1,8.5-3c99.3-79.8,144.7-154.3,165.3-202.8c5.6-12.1,9.9-24.7,12.8-37.7 c0.8-3.6,1.2-5.7,1.3-6.4c2.2-11.8,3.4-23.9,3.4-35.9C429.6,85.8,343.8,0,238.4,0z M399.6,222.4c0,0.2-0.1,0.4-0.1,0.6 c-0.1,0.5-0.4,2-0.9,4.3c0,0.1,0,0.1,0,0.2c-2.5,11.2-6.2,22.1-11.1,32.6c-0.1,0.1-0.1,0.3-0.2,0.4 c-18.7,44.3-59.7,111.9-148.9,185.6c-89.2-73.7-130.2-141.3-148.9-185.6c-0.1-0.1-0.1-0.3-0.2-0.4c-4.8-10.4-8.5-21.4-11.1-32.6 c0-0.1,0-0.1,0-0.2c-0.6-2.3-0.8-3.8-0.9-4.3c0-0.2-0.1-0.4-0.1-0.7c-2-10.3-3-20.7-3-31.2c0-90.5,73.7-164.2,164.2-164.2 s164.2,73.7,164.2,164.2C402.6,201.7,401.6,212.2,399.6,222.4z" />
                                    <path
                                        d="M238.4,71.9c-66.9,0-121.4,54.5-121.4,121.4s54.5,121.4,121.4,121.4s121.4-54.5,121.4-121.4S305.3,71.9,238.4,71.9z M238.4,287.7c-52.1,0-94.4-42.4-94.4-94.4s42.4-94.4,94.4-94.4s94.4,42.4,94.4,94.4S290.5,287.7,238.4,287.7z" />
                                </g>
                            </g>
                            <g> </g>
                            <g> </g>
                            <g> </g>
                            <g> </g>
                            <g> </g>
                            <g> </g>
                            <g> </g>
                            <g> </g>
                            <g> </g>
                            <g> </g>
                            <g> </g>
                            <g> </g>
                            <g> </g>
                            <g> </g>
                            <g> </g>
                        </svg>
                        Lô E1-1 Võ Nguyên Giáp - P. Phú Thứ - </br>Q. Cái Răng - TP. Cần Thơ
                    </li>
                    <li><svg version="1.1" id="Capa_1" xmlns="http://www.w3.org/2000/svg"
                            xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" width="32.666px"
                            height="32.666px" viewBox="0 0 32.666 32.666"
                            style="enable-background:new 0 0 32.666 32.666;" xml:space="preserve">
                            <g>
                                <path
                                    d="M28.189,16.504h-1.666c0-5.437-4.422-9.858-9.856-9.858l-0.001-1.664C23.021,4.979,28.189,10.149,28.189,16.504z M16.666,7.856L16.665,9.52c3.853,0,6.983,3.133,6.981,6.983l1.666-0.001C25.312,11.735,21.436,7.856,16.666,7.856z M16.333,0 C7.326,0,0,7.326,0,16.334c0,9.006,7.326,16.332,16.333,16.332c0.557,0,1.007-0.45,1.007-1.006c0-0.559-0.45-1.01-1.007-1.01 c-7.896,0-14.318-6.424-14.318-14.316c0-7.896,6.422-14.319,14.318-14.319c7.896,0,14.317,6.424,14.317,14.319 c0,3.299-1.756,6.568-4.269,7.954c-0.913,0.502-1.903,0.751-2.959,0.761c0.634-0.377,1.183-0.887,1.591-1.529 c0.08-0.121,0.186-0.228,0.238-0.359c0.328-0.789,0.357-1.684,0.555-2.518c0.243-1.064-4.658-3.143-5.084-1.814 c-0.154,0.492-0.39,2.048-0.699,2.458c-0.275,0.366-0.953,0.192-1.377-0.168c-1.117-0.952-2.364-2.351-3.458-3.457l0.002-0.001 c-0.028-0.029-0.062-0.061-0.092-0.092c-0.031-0.029-0.062-0.062-0.093-0.092v0.002c-1.106-1.096-2.506-2.34-3.457-3.459 c-0.36-0.424-0.534-1.102-0.168-1.377c0.41-0.311,1.966-0.543,2.458-0.699c1.326-0.424-0.75-5.328-1.816-5.084 c-0.832,0.195-1.727,0.227-2.516,0.553c-0.134,0.057-0.238,0.16-0.359,0.24c-2.799,1.774-3.16,6.082-0.428,9.292 c1.041,1.228,2.127,2.416,3.245,3.576l-0.006,0.004c0.031,0.031,0.063,0.06,0.095,0.09c0.03,0.031,0.059,0.062,0.088,0.095 l0.006-0.006c1.16,1.118,2.535,2.765,4.769,4.255c4.703,3.141,8.312,2.264,10.438,1.098c3.67-2.021,5.312-6.338,5.312-9.719 C32.666,7.326,25.339,0,16.333,0z" />
                            </g>
                            <g> </g>
                            <g> </g>
                            <g> </g>
                            <g> </g>
                            <g> </g>
                            <g> </g>
                            <g> </g>
                            <g> </g>
                            <g> </g>
                            <g> </g>
                            <g> </g>
                            <g> </g>
                            <g> </g>
                            <g> </g>
                            <g> </g>
                        </svg>
                        Điện thoại: <?php echo isset($contact['hotline']) ? $contact['hotline'] : '0919 905 293'; ?>
                    </li>
                    <li><svg version="1.1" id="Capa_1" xmlns="http://www.w3.org/2000/svg"
                            xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 483.3 483.3"
                            style="enable-background:new 0 0 483.3 483.3;" xml:space="preserve">
                            <g>
                                <g>
                                    <path
                                        d="M424.3,57.75H59.1c-32.6,0-59.1,26.5-59.1,59.1v249.6c0,32.6,26.5,59.1,59.1,59.1h365.1c32.6,0,59.1-26.5,59.1-59.1 v-249.5C483.4,84.35,456.9,57.75,424.3,57.75z M456.4,366.45c0,17.7-14.4,32.1-32.1,32.1H59.1c-17.7,0-32.1-14.4-32.1-32.1v-249.5 c0-17.7,14.4-32.1,32.1-32.1h365.1c17.7,0,32.1,14.4,32.1,32.1v249.5H456.4z" />
                                    <path
                                        d="M304.8,238.55l118.2-106c5.5-5,6-13.5,1-19.1c-5-5.5-13.5-6-19.1-1l-163,146.3l-31.8-28.4c-0.1-0.1-0.2-0.2-0.2-0.3 c-0.7-0.7-1.4-1.3-2.2-1.9L78.3,112.35c-5.6-5-14.1-4.5-19.1,1.1c-5,5.6-4.5,14.1,1.1,19.1l119.6,106.9L60.8,350.95 c-5.4,5.1-5.7,13.6-0.6,19.1c2.7,2.8,6.3,4.3,9.9,4.3c3.3,0,6.6-1.2,9.2-3.6l120.9-113.1l32.8,29.3c2.6,2.3,5.8,3.4,9,3.4 c3.2,0,6.5-1.2,9-3.5l33.7-30.2l120.2,114.2c2.6,2.5,6,3.7,9.3,3.7c3.6,0,7.1-1.4,9.8-4.2c5.1-5.4,4.9-14-0.5-19.1L304.8,238.55z" />
                                </g>
                            </g>
                            <g> </g>
                            <g> </g>
                            <g> </g>
                            <g> </g>
                            <g> </g>
                            <g> </g>
                            <g> </g>
                            <g> </g>
                            <g> </g>
                            <g> </g>
                            <g> </g>
                            <g> </g>
                            <g> </g>
                            <g> </g>
                            <g> </g>
                        </svg>
                        Email: <?php echo isset($contact['email']) ? $contact['email'] : '<EMAIL>'; ?>
                    </li>
                    <li><svg version="1.1" id="Capa_1" xmlns="http://www.w3.org/2000/svg"
                            xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" width="460.298px"
                            height="460.297px" viewBox="0 0 460.298 460.297"
                            style="enable-background:new 0 0 460.298 460.297;" xml:space="preserve">
                            <g>
                                <g>
                                    <path
                                        d="M230.149,120.939L65.986,256.274c0,0.191-0.048,0.472-0.144,0.855c-0.094,0.38-0.144,0.656-0.144,0.852v137.041 c0,4.948,1.809,9.236,5.426,12.847c3.616,3.613,7.898,5.431,12.847,5.431h109.63V303.664h73.097v109.64h109.629 c4.948,0,9.236-1.814,12.847-5.435c3.617-3.607,5.432-7.898,5.432-12.847V257.981c0-0.76-0.104-1.334-0.288-1.707L230.149,120.939 z" />
                                    <path
                                        d="M457.122,225.438L394.6,173.476V56.989c0-2.663-0.856-4.853-2.574-6.567c-1.704-1.712-3.894-2.568-6.563-2.568h-54.816 c-2.666,0-4.855,0.856-6.57,2.568c-1.711,1.714-2.566,3.905-2.566,6.567v55.673l-69.662-58.245 c-6.084-4.949-13.318-7.423-21.694-7.423c-8.375,0-15.608,2.474-21.698,7.423L3.172,225.438c-1.903,1.52-2.946,3.566-3.14,6.136 c-0.193,2.568,0.472,4.811,1.997,6.713l17.701,21.128c1.525,1.712,3.521,2.759,5.996,3.142c2.285,0.192,4.57-0.476,6.855-1.998 L230.149,95.817l197.57,164.741c1.526,1.328,3.521,1.991,5.996,1.991h0.858c2.471-0.376,4.463-1.43,5.996-3.138l17.703-21.125 c1.522-1.906,2.189-4.145,1.991-6.716C460.068,229.007,459.021,226.961,457.122,225.438z" />
                                </g>
                            </g>
                            <g> </g>
                            <g> </g>
                            <g> </g>
                            <g> </g>
                            <g> </g>
                            <g> </g>
                            <g> </g>
                            <g> </g>
                            <g> </g>
                            <g> </g>
                            <g> </g>
                            <g> </g>
                            <g> </g>
                            <g> </g>
                            <g> </g>
                        </svg>
                        Website: hondaotocantho.com.vn
                    </li>
                </ul>
            </div>
            <div class="col col-4">
                <h3>Thông tin mới</h3>
                <?php
                    $sql = "SELECT * FROM entry a 
                            JOIN category b ON a.category_id = b.category_id 
                            WHERE type = 1 
                            ORDER BY date_post DESC 
                            LIMIT 3";

                    $result = mysql_query($sql);

                    while($row = mysql_fetch_array($result)) {
                        $url = utf8tourl(utf8convert($row['title']));
                        $image = !empty($row['image']) ? $row['image'] : 'images/noimage.jpg';
                        $title = htmlspecialchars($row['title']);
                        ?>

                <a class="fnew" href="/<?= $url ?>">
                    <img src="<?= htmlspecialchars($image) ?>" alt="<?= $title ?>">
                    <h4><?= $title ?></h4>
                </a>

                <?php } ?>
            </div>


            <div class="col col-4">
                <!-- <h3>Fanpage</h3>
                    <div class="fb-page" data-href="https://www.facebook.com/Xeotomientaygiare/" data-width="500" data-height="250" data-small-header="false" data-adapt-container-width="true" data-hide-cover="false" data-show-facepile="false">
                    </div> -->
            </div>
        </div>
    </div>
    <div class="copyright">
        <div class="container">© 2024 hondaotocantho.com.vn.</div>
    </div>
</footer>
<script>

</script>
</body>

</html>