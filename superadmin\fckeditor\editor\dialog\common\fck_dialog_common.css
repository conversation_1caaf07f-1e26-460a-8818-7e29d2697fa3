/*
 * FCKeditor - The text editor for Internet - http://www.fckeditor.net
 * Copyright (C) 2003-2010 <PERSON><PERSON>
 *
 * == BEGIN LICENSE ==
 *
 * Licensed under the terms of any of the following licenses at your
 * choice:
 *
 *  - GNU General Public License Version 2 or later (the "GPL")
 *    http://www.gnu.org/licenses/gpl.html
 *
 *  - GNU Lesser General Public License Version 2.1 or later (the "LGPL")
 *    http://www.gnu.org/licenses/lgpl.html
 *
 *  - Mozilla Public License Version 1.1 or later (the "MPL")
 *    http://www.mozilla.org/MPL/MPL-1.1.html
 *
 * == END LICENSE ==
 *
 * This is the CSS file used for interface details in some dialog
 * windows.
 */

/* #########
 *  WARNING
 * #########
 * When changing this file, the minified version of it must be updated in the
 * fck_dialog_common.js file (see GetCommonDialogCss).
 */

.ImagePreviewArea
{
	border: #000000 1px solid;
	overflow: auto;
	width: 100%;
	height: 170px;
	background-color: #ffffff;
}

.FlashPreviewArea
{
	border: #000000 1px solid;
	padding: 5px;
	overflow: auto;
	width: 100%;
	height: 170px;
	background-color: #ffffff;
}

.BtnReset
{
	float: left;
	background-position: center center;
	background-image: url(images/reset.gif);
	width: 16px;
	height: 16px;
	background-repeat: no-repeat;
	border: 1px none;
	font-size: 1px ;
}

.BtnLocked, .BtnUnlocked
{
	float: left;
	background-position: center center;
	background-image: url(images/locked.gif);
	width: 16px;
	height: 16px;
	background-repeat: no-repeat;
	border: none 1px;
	font-size: 1px ;
}

.BtnUnlocked
{
	background-image: url(images/unlocked.gif);
}

.BtnOver
{
	border: outset 1px;
	cursor: pointer;
	cursor: hand;
}
